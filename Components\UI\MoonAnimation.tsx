import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface MoonAnimationProps {
  isNightMode: boolean; // true quand mode = 'night'
  currentMode: string;  // pour détecter les changements de mode
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ isNightMode, currentMode }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const haloRef = useRef<HTMLDivElement>(null); // 🔧 CISCO: Référence séparée pour le halo
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Tween | null>(null);

  useEffect(() => {
    if (!moonRef.current || !haloRef.current) return;

    // 🌙 CISCO: Mode Nuit profonde - Apparition et descente de la lune
    if (isNightMode && currentMode === 'night') {
      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }

      // 🔧 CISCO: Position initiale de la lune - haut gauche de l'écran
      gsap.set(moonRef.current, {
        x: '15vw', // Plus à gauche pour commencer la courbe
        y: '10vh', // Plus haut pour la courbe parabolique
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        scale: 1,
        display: 'block'
      });

      // Position initiale du halo (même position que la lune)
      gsap.set(haloRef.current, {
        x: '15vw',
        y: '10vh',
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        display: 'block'
      });

      // Créer la timeline d'animation
      animationRef.current = gsap.timeline();

      // Phase 1: Apparition douce de la lune et du halo (3 secondes)
      animationRef.current.to(moonRef.current, {
        opacity: 1.0, // 🔧 CISCO: Plus lumineuse (était 0.9)
        duration: 3,
        ease: "power2.out"
      });

      // Apparition du halo en parallèle - plus lumineux
      animationRef.current.to(haloRef.current, {
        opacity: 0.25, // 🔧 CISCO: Halo plus visible (était 0.15)
        duration: 3,
        ease: "power2.out"
      }, 0); // En même temps que la lune

      // 🔧 CISCO: Phase 2: Mouvement en courbe parabolique du haut-gauche vers bas-droite
      // Utilisation de keyframes pour créer une courbe parabolique naturelle
      // Durée totale : 120 secondes (2 minutes) pour un mouvement lent et naturel

      // Animation de la lune avec courbe parabolique
      animationRef.current.to(moonRef.current, {
        keyframes: [
          { x: '15vw', y: '10vh', duration: 0 }, // Point de départ
          { x: '30vw', y: '8vh', duration: 0.2 }, // Légère montée
          { x: '50vw', y: '5vh', duration: 0.4 }, // Sommet de la courbe
          { x: '70vw', y: '25vh', duration: 0.7 }, // Descente
          { x: '95vw', y: '70vh', duration: 1.0 } // 🔧 CISCO: Plus à droite (95vw au lieu de 85vw)
        ],
        duration: 120, // 2 minutes pour un mouvement lent et naturel
        ease: "power1.inOut" // Easing naturel pour la courbe
      }, 3); // Commence après l'apparition

      // Mouvement du halo synchronisé avec la lune
      animationRef.current.to(haloRef.current, {
        keyframes: [
          { x: '15vw', y: '10vh', duration: 0 }, // Point de départ
          { x: '30vw', y: '8vh', duration: 0.2 }, // Légère montée
          { x: '50vw', y: '5vh', duration: 0.4 }, // Sommet de la courbe
          { x: '70vw', y: '25vh', duration: 0.7 }, // Descente
          { x: '95vw', y: '70vh', duration: 1.0 } // 🔧 CISCO: Plus à droite (95vw au lieu de 85vw)
        ],
        duration: 120,
        ease: "power1.inOut"
      }, 3); // Synchronisé avec la lune

    } else if (!isNightMode && currentMode !== 'night') {
      // Arrêter l'animation de descente
      if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }

      // Si la lune est visible, la faire disparaître en douceur avec le halo
      if (moonRef.current && gsap.getProperty(moonRef.current, "opacity") > 0) {
        fadeOutRef.current = gsap.timeline();

        // Disparition de la lune
        fadeOutRef.current.to(moonRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in"
        });

        // Disparition du halo en parallèle
        fadeOutRef.current.to(haloRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in",
          onComplete: () => {
            if (moonRef.current && haloRef.current) {
              gsap.set(moonRef.current, { display: 'none' });
              gsap.set(haloRef.current, { display: 'none' });
            }
          }
        }, 0); // En même temps que la lune
      } else {
        // Si déjà invisible, juste les cacher
        gsap.set(moonRef.current, { display: 'none' });
        gsap.set(haloRef.current, { display: 'none' });
      }
    }

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [isNightMode, currentMode]);

  return (
    <>
      {/* 🌙 CISCO: Halo lumineux séparé pour éviter l'effet carré */}
      <div
        ref={haloRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '200px',
          height: '200px',
          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 30%, rgba(255, 255, 255, 0.04) 60%, transparent 100%)', // 🔧 CISCO: Halo plus lumineux
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />

      {/* 🌙 CISCO: Lune principale */}
      <div
        ref={moonRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '120px',
          height: '120px',
          backgroundImage: 'url(/Lune-Moon.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          filter: 'brightness(1.6) contrast(1.3)', // 🔧 CISCO: Plus lumineuse (était 1.3/1.1)
        }}
        title="🌙 Lune nocturne"
      />
    </>
  );
};

export default MoonAnimation;
