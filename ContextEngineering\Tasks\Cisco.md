
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
ContextEngineering\Tasks\Cisco.md


Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.

**Alors attention, gardez bien ça en mémoire, le DOM élément très important, mettez ça en mémoire, parce que pour le soleil, tout à l'heure, on va corriger le soleil, et à mon avis, il va être au même endroit, dans Astronomical Layer. Voilà, donc pareil pour le soleil, le soleil, à mon avis, il faudra le mettre dans le même DOM élément.** 



Ok là ça va être radical Écoutez bien les instructions elles sont très précises Nous allons supprimer tout le code qui consiste à relever l'horaire du PC géolocalisation Vous allez aussi supprimer la petite div qui explique le ciel dynamique Vous pouvez le supprimer ça aussi où c'est marqué Le ciel change automatiquement selon l'heure de votre PC etc Et nous allons garder que le mode manuel Ok on supprime temps simulé le GPS lever coucher etc position GPS tout ça l'heure manuelle vous supprimez tout ce qui risque de faire des conflits Parce que je vais vous dire une chose on va garder que les modes d'arrière-plan en mode manuel nuit profonde aube lever de soleil matin 12h Zénith Après-Midi Coucher de soleil crépuscule C'est tout On dira simplement aux utilisateurs quand il actualise la page Vous réglez sur 12 h Voilà comme ça après s'il veut le changer ils changeront et cliqueront sur les boutons parce que là c'est trop compliqué à mettre en place ça crée trop de conflits  

















































































































